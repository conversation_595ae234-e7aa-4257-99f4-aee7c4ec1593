# FastAPI + React Movie List Application

This is a converted version of the original FastAPI + HTMX application, now using React as the frontend and FastAPI as an API backend.

## Project Structure

```
fastapi-react/
├── backend/
│   ├── main.py
│   └── requirements.txt
├── frontend/
│   └── (React TypeScript app)
└── README.md
```

## Setup Instructions

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the FastAPI server:
   ```bash
   uvicorn main:app --reload --port 8000
   ```

The API will be available at http://localhost:8000

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the React development server:
   ```bash
   npm start
   ```

The React app will be available at http://localhost:3000

## API Endpoints

- `GET /` - Root endpoint with welcome message
- `GET /api/films` - Returns a list of films in JSON format

## Features

- React frontend with TypeScript
- FastAPI backend with CORS enabled
- Axios for API communication
- Load more button functionality
- Responsive table design

## Original Application

This is a converted version of the HTMX-based application located in `../fastapi-htmx/`