from typing import List
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class Film(BaseModel):
    name: str
    director: str

@app.get("/api/films", response_model=List[Film])
async def get_films():
    films = [
        {'name': 'Blade Runner', 'director': '<PERSON>'},
        {'name': 'Pulp Fiction', 'director': '<PERSON>'},
        {'name': 'Mulholland Drive', 'director': '<PERSON>'},
    ]
    return films

@app.get("/")
async def root():
    return {"message": "FastAPI + React Movie List API"}