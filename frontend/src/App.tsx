import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './App.css';

interface Film {
  name: string;
  director: string;
}

function App() {
  const [films, setFilms] = useState<Film[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFilms();
  }, []);

  const fetchFilms = async () => {
    try {
      const response = await axios.get<Film[]>('http://localhost:8000/api/films');
      setFilms(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching films:', error);
      setLoading(false);
    }
  };

  const loadMore = () => {
    fetchFilms();
  };

  if (loading) {
    return <div className="container">Loading...</div>;
  }

  return (
    <div className="container">
      <h1 className="title">Movie List</h1>
      
      <table className="movie-table">
        <thead>
          <tr>
            <th>Film Name</th>
            <th>Director</th>
          </tr>
        </thead>
        <tbody>
          {films.map((film, index) => (
            <tr key={index}>
              <td>{film.name}</td>
              <td>{film.director}</td>
            </tr>
          ))}
        </tbody>
      </table>
      
      <button className="load-more-btn" onClick={loadMore}>
        Load More
      </button>
    </div>
  );
}

export default App;
